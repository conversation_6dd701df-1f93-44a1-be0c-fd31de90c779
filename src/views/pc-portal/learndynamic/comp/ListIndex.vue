<template>
  <div class="com-pc-portal-container">
    <n-spin :show="loading">
      <div v-if="tableData.length > 0">
        <div :class="$style.cardWrap" v-for="(item, index) in tableData" :key="index" @click="handleClick(item)">
          <div class="min-w-[300px] h-[180px] border-[1px] mr-[24px]">
            <!-- object-cover 保持图片比例 -->
            <img
              v-if="item.fmtFileAttachmentList != '' || item.fmtFileAttachmentList.length > 0"
              :src="getPahtUrl(item.fmtFileAttachmentList[0].filePath)"
              alt="封面"
              class="w-full h-full"
            />
          </div>
          <div>
            <div class="text-[24px] font-[600] mb-[8px]">{{ item.bt }}</div>
            <div class="text-[14px] font-[400] mb-[8px] text-[#999999FF]">{{ item.fbsj }}</div>
            <div class="text-[16px] font-[400] mb-[8px] text-[#4D4D4DFF]">
              <div :class="$style.lineClamp" v-html="processHtmlContent(item.zw)"></div>
            </div>
          </div>
        </div>
      </div>
      <div class="text-center h-[285px]" v-else>
        <ComEmpty />
      </div>
    </n-spin>
    <PaginationComp class="justify-end pt-[10px]" />
  </div>
</template>

<script setup lang="ts">
import { useNaivePagination, useNPaginationComp } from '@/utils/useNaivePagination.ts';
import { onMounted, ref } from 'vue';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { IObj } from '@/types';
import { useRouter } from 'vue-router';
import { showNewsAPI } from '../fetchData';
import ComEmpty from '@/components/empty/index.vue';
// import ComEmpty from '@/views/pc-portal/common/comp/Empty.vue';
import { AkImage } from '@kalimahapps/vue-icons';
import { size } from 'lodash-es';

const router = useRouter();
const { pagination, updateTotal } = useNaivePagination(getTableData);
const PaginationComp = useNPaginationComp(pagination);
pagination.pageSize = 3;

const [loading, search] = useAutoLoading(true);
let filterData: IObj<any> = {}; // 搜索条件

const tableData = ref<any[]>([]);
function getTableData() {
  // console.log(pagination.page, pagination.pageSize);
  const params = { ...filterData, pageNo: pagination.page, pageSize: pagination.pageSize };
  console.log(params, '-=-=-=-=-');
  search(showNewsAPI(params)).then((res) => {
    tableData.value = res.data.rows || [];
    updateTotal(res.data.total || 0);
  });
}

// 图片地址
function getPahtUrl(val: string) {
  return window.$SYS_CFG.apiBaseFile + val;
}

function getTableDataWrap(data: IObj<any>) {
  filterData = Object.assign({}, data) || {};
  pagination.page = 1;
  getTableData();
}
function handleClick(item: IObj<any>) {
  router.push({
    name: 'pcPortalLearnDynamicDetails',
    query: {
      id: item.id,
    },
  });
}
// 处理 HTML 内容
function processHtmlContent(html: string): string {
  if (!html) return '';
  // 创建临时 div 来处理 HTML 字符串
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = html;
  // 查找所有图片元素，并隐藏
  const images = tempDiv.querySelectorAll('img');
  images.forEach((img) => {
    img.style.display = 'none';
  });
  return tempDiv.innerHTML;
}

defineExpose({
  getTableDataWrap,
});

defineOptions({ name: 'LearnDynamicListIndex' });
</script>

<style module lang="scss">
.cardWrap {
  // width: 1400px;
  height: 228px;
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;
  display: flex;
  padding: 24px;
  margin: 24px 0;
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  }
}
.lineClamp {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3; /* 限制显示3行 */
  line-clamp: 3; /* 限制显示3行 */
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.5;
}
</style>
