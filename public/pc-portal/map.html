<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title></title>
    <style>
        html, body {
            margin: 0;
            padding: 0;
        }

        .map {
            width: 100vw;
            height: 100vh;
            border-radius: 4px;
        }
    </style>

    <script src="https://api.map.baidu.com/api?type=webgl&v=1.0&ak=x1tamyHbuQi8CrNx7g9WeTGj4zNqxRy5"></script>
    <script>
        window.onload = function () {
            const map = new BMapGL.Map("JS_Map", {enableIconClick: true});
            // 地点：北京市海淀区清华大学刘卿楼1001
            const point = {
                "lng": 116.3425617398692,
                "lat": 40.00912091308479
            };

            // 标记
            map.centerAndZoom(point, 18);
            map.addOverlay(new BMapGL.Marker(point));

            // 视角
            map.enableScrollWheelZoom(true);
            map.setHeading(0);
            map.setTilt(45);

            // 添加比例尺控件
            const scaleControl = new BMapGL.ScaleControl({
                anchor: BMAP_ANCHOR_BOTTOM_LEFT,
                offset: new BMapGL.Size(10, 10)
            });
            map.addControl(scaleControl);

            // 全局变量记录插入的jsonp标签
            window.clickPoint = null;
            window.script = null;
            window.success = function (res) {
                if (res.uii_err === 0 && res.content) {
                    const info = res.content;
                    const sContent = `<h4 style='margin:0 0 5px 10px;'>${info.name}</h4>`;
                    const infoWindow = new BMapGL.InfoWindow(sContent);

                    map.openInfoWindow(infoWindow, clickPoint);
                    document.getElementsByTagName('head')[0].removeChild(script);
                }
            }

            // poi点
            map.addEventListener('click', e => {
                clickPoint = e.latlng;
                const itemId = map.getIconByClickPosition(e);

                if (itemId) {
                    let url = "https://api.map.baidu.com/?qt=inf&uid=" + itemId.uid + '&operate=mapclick&clicktype=tile&ie=utf-8&oue=1&fromproduct=jsapi&res=api&&ak=x1tamyHbuQi8CrNx7g9WeTGj4zNqxRy5&callback=success'
                    script = document.createElement('script');
                    script.setAttribute('src', url);
                    document.getElementsByTagName('head')[0].appendChild(script);
                }
            });
        }
    </script>
</head>
<body>
<div id="JS_Map" class="map"></div>
</body>
</html>