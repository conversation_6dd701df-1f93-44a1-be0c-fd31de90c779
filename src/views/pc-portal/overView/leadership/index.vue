<template>
  <div :class="$style.leadershipPage">
    <ComBread :data="breadData" />
    <div class="flex">
      <div :class="$style.catalogue">
        <div :class="$style.title">目录</div>
        <n-scrollbar style="height: 100%">
          <div
            v-for="page in pageList"
            :key="page.id"
            :class="[$style.categoryItem, { [$style.active]: activeCategory === page.bt }]"
            @click="scrollToSection(page.id, page.bt)"
          >
            {{ page.bt }}
          </div>
        </n-scrollbar>
      </div>
      <div :class="$style.content">
        <div :class="$style.title">学会章程</div>
        <n-scrollbar ref="scrollbarRef" style="max-height: calc(100vh - 108px - 72px - 64px - 32px - 72px)">
          <div>
            <section v-for="item in pageList" :id="item.id" :key="item.id">
              <div :class="$style.bt">{{ item.bt }}</div>
              <div v-html="item.zw" :class="$style.zw"></div>
            </section>
          </div>
        </n-scrollbar>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import type { ScrollbarInst } from 'naive-ui';
import type { IBreadData } from '@/types';
import ComBread from '@/components/breadcrumb/ComBread.vue';
import { showPageListAPI } from './fetchData';
import type { Response, PortalSocietyConstitutionPageVo, PageModelPortalSocietyConstitutionPageVo } from './type';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/utils/useNaivePagination.ts';

const scrollbarRef = ref<ScrollbarInst>();
const pageList = ref<PortalSocietyConstitutionPageVo[]>([]);
const [loading, search] = useAutoLoading(true);
const { updateTotal } = useNaivePagination(getPageContent);
const activeCategory = ref<string | undefined>('');
const breadData: IBreadData[] = [
  { name: '学会概况', routeRaw: { path: 'overview' }, clickable: true },
  { name: '学会章程' },
];

const scrollToSection = (sectionId: any, value: string | undefined) => {
  activeCategory.value = value;
  const element = document.getElementById(sectionId);

  if (element && scrollbarRef.value) {
    // 获取元素相对于其父容器的 offsetTop
    const elementTop = element.offsetTop;

    // 使用 Naive UI scrollbar 的 scrollTo 方法
    scrollbarRef.value.scrollTo({
      top: elementTop,
      behavior: 'smooth',
    });
  }
};
// 获取右侧文章
function getPageContent() {
  let params = {
    pageNo: 1,
    pageSize: -1,
  };
  search(
    showPageListAPI(params).then((res: Response) => {
      pageList.value = (res.data?.rows || []).sort((a, b) => (a.px || 0) - (b.px || 0));
      updateTotal(res.data?.total || 0);
      activeCategory.value = pageList.value[0].bt || '';
    })
  );
}

onMounted(() => {
  getPageContent();
});

defineOptions({ name: 'LeadershipIndex' });
</script>

<style module lang="scss">
.leadershipPage {
  width: 100%;

  .title {
    font-family:
      Alibaba PuHuiTi 2,
      Alibaba PuHuiTi 20;
    font-weight: 700;
    font-size: 26px;
    color: #212121;
    margin-bottom: 20px;
  }
  .catalogue {
    width: 376px;
    height: 558px;
    padding: 24px;
    background: #ffffff;
    border-radius: 8px 8px 8px 8px;

    .categoryItem {
      height: 60px;
      padding-left: 30px;
      display: flex;
      align-items: center;
      font-weight: 400;
      font-size: 18px;
      color: #212121;
      cursor: pointer;
      transition: all 0.2s ease;
      border-radius: 4px;
      margin-bottom: 12px;

      &:hover {
        background: #e9ecef;
        color: #333;
      }

      &.active {
        background: #e0f2ff;
        color: #0c87e5;
        font-weight: 600;
        border: 1px solid #0c87e5;
      }
    }
  }

  .content {
    flex: 1;
    background: #ffffff;
    margin-left: 24px;
    padding: 32px 24px 0;
    border-radius: 8px 8px 8px 8px;

    .bt {
      font-weight: 700;
    }

    .zw {
      font-weight: 400;
    }
  }
}
</style>
