<template>
  <n-scrollbar x-scrollable>
    <div class="list" v-if="tableData.length > 0">
      <div class="li" v-for="item in tableData" :key="item.id" @click="toUrl(item.ljdz)">{{ item.bt || '--' }}</div>
    </div>
    <ComEmpty v-else />
  </n-scrollbar>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { showRelateLinkAPI } from '../fetchData';
import { $toast } from '@/common/shareContext';
import ComEmpty from '@/components/empty/index.vue';

const tableData: any = ref([]);
const getTable = () => {
  showRelateLinkAPI({ pageNo: 1, pageSize: -1, xslx: 1 }).then((res) => {
    // console.log('API1响应:', res);
    tableData.value = res.data.rows || [];
  });
};
getTable();
const toUrl = (url: string) => {
  console.log('跳转链接:', url);
  if (url) {
    window.open(url);
  } else {
    $toast.error('链接地址为空');
  }
};
defineOptions({ name: 'contentBox5TableIndex' });
</script>

<style scoped lang="scss">
.n-scrollbar {
  overflow-x: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE/Edge */

  &::-webkit-scrollbar {
    display: none; /* Chrome/Safari */
  }
}
.list {
  display: flex;
  gap: 10px; /* 改用 gap 控制间距 */
  padding: 10px 0;
  width: fit-content; /* 让宽度自适应子元素 */
  min-width: 100%; /* 确保至少占满父容器 */

  .li {
    cursor: pointer;
    flex: 0 0 260px; /* 固定宽度，不伸缩 */
    height: 80px;
    line-height: 80px;
    border-radius: 6px;
    margin-right: 5px; /* 右侧间距 */
    background-color: rgb(226, 237, 254);
    color: rgb(47, 148, 232);
    text-align: center;
  }
}
</style>
